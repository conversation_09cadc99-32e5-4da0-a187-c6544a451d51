'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import {
  ChevronDown,
  Pill,
  RefreshCw,
  User,
  Calendar,
  AlertCircle,
  Stethoscope,
  Phone,
  Focus,
  FileText,
  HeartHandshake,
  Activity,
  Shield,
  Video,
  Navigation,
  DollarSign,
  TrendingUp
} from 'lucide-react';
import { useProfile } from '@/hooks/use-profile';
import { useCareCanvasActions } from '@/hooks/use-care-canvas-actions';
import { useFocusAppointments } from '@/hooks/use-focus-appointments';
import { Prescription, CareTeamMember, Claim } from '@/types';
import { cn } from '@/lib/utils';

interface CareCanvasFullPageProps {
  className?: string;
}

export function CareCanvasFullPage({ className }: CareCanvasFullPageProps) {
  const { profile, isLoading, error } = useProfile();
  const {
    handleRefillRequest,
    handleAppointmentRequest,
    handleClaimInquiry,
    handleFindCare,
    handleDedicatedNurseLine,
    handleConditionTelehealth,
    handleTelehealthJoin,
    handleMedicationQuestion,
    handleMedicationReminders
  } = useCareCanvasActions();
  const { appointments } = useFocusAppointments();

  // Collapsible states - all open by default for full-page experience
  const [isFocusOpen, setIsFocusOpen] = useState(true);
  const [isCareTeamOpen, setIsCareTeamOpen] = useState(true);
  const [isRxOpen, setIsRxOpen] = useState(true);
  const [isClaimsOpen, setIsClaimsOpen] = useState(true);
  const [isConditionMgmtOpen, setIsConditionMgmtOpen] = useState(true);
  const [isPlanUsageOpen, setIsPlanUsageOpen] = useState(true);
  const [isWellnessOpen, setIsWellnessOpen] = useState(true);

  if (isLoading) {
    return (
      <div className={cn("min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8", className)}>
        <div className="max-w-7xl mx-auto">
          <div className="animate-pulse space-y-8">
            <div className="text-center space-y-4">
              <div className="h-8 bg-gray-200 dark:bg-gray-700 rounded w-1/3 mx-auto"></div>
              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2 mx-auto"></div>
            </div>
            <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="h-64 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn("min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 p-8", className)}>
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <AlertCircle className="h-12 w-12 text-red-500 mx-auto mb-4" />
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Unable to load your care information
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Please try refreshing the page or contact support if the problem persists.
            </p>
          </div>
        </div>
      </div>
    );
  }

  // Mock data for demonstration
  const prescriptions: Prescription[] = profile?.prescriptions || [
    {
      id: '1',
      name: 'Albuterol Inhaler',
      dosage: '90 mcg',
      frequency: '2 puffs every 4-6 hours as needed',
      refillsRemaining: 2,
      lastFilled: '2024-01-15',
      prescribedBy: 'Dr. Sarah Johnson'
    },
    {
      id: '2', 
      name: 'Montelukast',
      dosage: '10 mg',
      frequency: 'Once daily in the evening',
      refillsRemaining: 5,
      lastFilled: '2024-01-10',
      prescribedBy: 'Dr. Sarah Johnson'
    }
  ];

  const recentClaims: Claim[] = profile?.recentClaims || [
    {
      claimId: 'CLM001',
      claimDate: '2024-01-20',
      providerName: 'City Medical Center',
      procedureDescription: 'Annual Physical Exam',
      billedAmount: 250,
      status: 'Processed'
    },
    {
      claimId: 'CLM002', 
      claimDate: '2024-01-15',
      providerName: 'Respiratory Specialists',
      procedureDescription: 'Pulmonary Function Test',
      billedAmount: 180,
      status: 'Pending'
    }
  ];

  return (
    <div className={cn("min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800", className)}>
      {/* Hero Header */}
      <div className="bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-8 py-12">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
              Your Care Canvas
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Everything you need to manage your healthcare in one beautiful, organized space
            </p>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="max-w-7xl mx-auto px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-8">
          
          {/* Focus Section */}
          <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <Collapsible open={isFocusOpen} onOpenChange={setIsFocusOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <Focus className="h-6 w-6 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div>
                        <span className="text-lg">Focus</span>
                        <Badge variant="secondary" className="ml-3">
                          {appointments.length}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-5 w-5 transition-transform duration-200",
                      isFocusOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {appointments.length === 0 ? (
                    <div className="text-center py-8">
                      <Focus className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No items to focus on right now
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {appointments.map((appointment) => (
                        <div
                          key={appointment.id}
                          className="p-4 rounded-xl bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border border-blue-200 dark:border-blue-700/30"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h3 className="font-semibold text-base mb-1">
                                {appointment.type === 'telehealth' ? 'Telehealth' : 'In-Person'} Appointment
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-slate-300">
                                {appointment.provider} • {appointment.specialty}
                              </p>
                              <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-slate-400 mt-2">
                                <Calendar className="h-4 w-4" />
                                <span>{appointment.date} at {appointment.time}</span>
                              </div>
                            </div>
                          </div>
                          {appointment.type === 'telehealth' && (
                            <Button
                              onClick={handleTelehealthJoin}
                              className="w-full bg-blue-600 hover:bg-blue-700 text-white"
                            >
                              <Video className="h-4 w-4 mr-2" />
                              Join Telehealth
                            </Button>
                          )}
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Care Team Section */}
          <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <Collapsible open={isCareTeamOpen} onOpenChange={setIsCareTeamOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-emerald-50 dark:hover:bg-emerald-900/20 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg">
                        <Stethoscope className="h-6 w-6 text-emerald-600 dark:text-emerald-400" />
                      </div>
                      <div>
                        <span className="text-lg">Care Team</span>
                        <Badge variant="secondary" className="ml-3">
                          {profile?.careTeam?.length || 0}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-5 w-5 transition-transform duration-200",
                      isCareTeamOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {!profile?.careTeam || profile.careTeam.length === 0 ? (
                    <div className="text-center py-8">
                      <Stethoscope className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400 mb-4">
                        No care team members found
                      </p>
                      <Button onClick={handleFindCare} variant="outline" className="border-emerald-200 text-emerald-700 hover:bg-emerald-50">
                        <Navigation className="h-4 w-4 mr-2" />
                        Find Care
                      </Button>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {profile.careTeam.map((member) => (
                        <div
                          key={member.providerId}
                          className="p-4 rounded-xl bg-gradient-to-r from-emerald-50 to-teal-50 dark:from-emerald-900/20 dark:to-teal-900/20 border border-emerald-200 dark:border-emerald-700/30"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="font-semibold text-base">
                                  {member.name}
                                </h3>
                                {member.isPrimary && (
                                  <Badge variant="default" className="bg-emerald-100 text-emerald-800 text-xs">
                                    Primary
                                  </Badge>
                                )}
                              </div>
                              <p className="text-sm text-gray-600 dark:text-slate-300 mb-2">
                                {member.specialty}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-slate-400">
                                {member.location}
                              </p>
                            </div>
                            <Button
                              onClick={() => handleAppointmentRequest(member.name, member.specialty)}
                              size="sm"
                              className="bg-emerald-600 hover:bg-emerald-700 text-white"
                            >
                              <Calendar className="h-4 w-4 mr-1" />
                              Schedule
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Prescriptions Section */}
          <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <Collapsible open={isRxOpen} onOpenChange={setIsRxOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-indigo-50 dark:hover:bg-indigo-900/20 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-indigo-100 dark:bg-indigo-900/30 rounded-lg">
                        <Pill className="h-6 w-6 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div>
                        <span className="text-lg">Prescriptions</span>
                        <Badge variant="secondary" className="ml-3">
                          {prescriptions.length}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-5 w-5 transition-transform duration-200",
                      isRxOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {prescriptions.length === 0 ? (
                    <div className="text-center py-8">
                      <Pill className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No prescriptions found
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {prescriptions.map((prescription) => (
                        <div
                          key={prescription.id}
                          className="p-4 rounded-xl bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 border border-indigo-200 dark:border-indigo-700/30"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h3 className="font-semibold text-base mb-1">
                                {prescription.name}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-slate-300 mb-1">
                                {prescription.frequency}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-slate-400">
                                {prescription.refillsRemaining} refills remaining
                              </p>
                            </div>
                            <Button
                              onClick={() => handleRefillRequest(prescription.name)}
                              size="sm"
                              className="bg-indigo-600 hover:bg-indigo-700 text-white"
                            >
                              <RefreshCw className="h-4 w-4 mr-1" />
                              Refill
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Recent Claims Section */}
          <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <Collapsible open={isClaimsOpen} onOpenChange={setIsClaimsOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-violet-50 dark:hover:bg-violet-900/20 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-violet-100 dark:bg-violet-900/30 rounded-lg">
                        <FileText className="h-6 w-6 text-violet-600 dark:text-violet-400" />
                      </div>
                      <div>
                        <span className="text-lg">Recent Claims</span>
                        <Badge variant="secondary" className="ml-3">
                          {recentClaims.length}
                        </Badge>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-5 w-5 transition-transform duration-200",
                      isClaimsOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  {recentClaims.length === 0 ? (
                    <div className="text-center py-8">
                      <FileText className="h-12 w-12 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <p className="text-gray-500 dark:text-gray-400">
                        No recent claims found
                      </p>
                    </div>
                  ) : (
                    <div className="space-y-4">
                      {recentClaims.map((claim, index) => (
                        <div
                          key={`${claim.claimId}-${index}`}
                          className="p-4 rounded-xl bg-gradient-to-r from-violet-50 to-purple-50 dark:from-violet-900/20 dark:to-purple-900/20 border border-violet-200 dark:border-violet-700/30"
                        >
                          <div className="flex items-start justify-between mb-3">
                            <div className="flex-1">
                              <h3 className="font-semibold text-base mb-1">
                                {claim.procedureDescription}
                              </h3>
                              <p className="text-sm text-gray-600 dark:text-slate-300 mb-1">
                                {claim.providerName} • {claim.claimDate}
                              </p>
                              <div className="flex items-center gap-4 text-sm">
                                <span className="text-gray-500 dark:text-slate-400">
                                  Amount: ${claim.billedAmount}
                                </span>
                                <Badge
                                  variant={claim.status === 'Processed' ? 'default' : 'secondary'}
                                  className={cn(
                                    claim.status === 'Processed'
                                      ? 'bg-green-100 text-green-800'
                                      : 'bg-yellow-100 text-yellow-800'
                                  )}
                                >
                                  {claim.status}
                                </Badge>
                              </div>
                            </div>
                            <Button
                              onClick={() => handleClaimInquiry(claim.providerName, claim.claimDate)}
                              size="sm"
                              variant="outline"
                              className="border-violet-200 text-violet-700 hover:bg-violet-50"
                            >
                              <FileText className="h-4 w-4 mr-1" />
                              Details
                            </Button>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Condition Management Section */}
          <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <Collapsible open={isConditionMgmtOpen} onOpenChange={setIsConditionMgmtOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-orange-50 dark:hover:bg-orange-900/20 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg">
                        <Activity className="h-6 w-6 text-orange-600 dark:text-orange-400" />
                      </div>
                      <div>
                        <span className="text-lg">Condition Management</span>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-5 w-5 transition-transform duration-200",
                      isConditionMgmtOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 border border-orange-200 dark:border-orange-700/30">
                      <h3 className="font-semibold text-base mb-3">Asthma Management</h3>
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <Button
                          onClick={handleConditionTelehealth}
                          className="bg-orange-600 hover:bg-orange-700 text-white"
                        >
                          <Video className="h-4 w-4 mr-2" />
                          Telehealth
                        </Button>
                        <Button
                          onClick={handleDedicatedNurseLine}
                          variant="outline"
                          className="border-orange-200 text-orange-700 hover:bg-orange-50"
                        >
                          <Phone className="h-4 w-4 mr-2" />
                          Nurse Line
                        </Button>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

          {/* Wellness Section */}
          <Card className="shadow-lg hover:shadow-xl transition-shadow duration-300 border-0 bg-white/90 dark:bg-gray-800/90 backdrop-blur-sm">
            <Collapsible open={isWellnessOpen} onOpenChange={setIsWellnessOpen}>
              <CollapsibleTrigger asChild>
                <CardHeader className="cursor-pointer hover:bg-cyan-50 dark:hover:bg-cyan-900/20 transition-colors rounded-t-lg">
                  <CardTitle className="flex items-center justify-between">
                    <div className="flex items-center gap-3">
                      <div className="p-2 bg-cyan-100 dark:bg-cyan-900/30 rounded-lg">
                        <Shield className="h-6 w-6 text-cyan-600 dark:text-cyan-400" />
                      </div>
                      <div>
                        <span className="text-lg">Wellness & Prevention</span>
                      </div>
                    </div>
                    <ChevronDown className={cn(
                      "h-5 w-5 transition-transform duration-200",
                      isWellnessOpen ? "rotate-180" : ""
                    )} />
                  </CardTitle>
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-4">
                  <div className="space-y-4">
                    <div className="p-4 rounded-xl bg-gradient-to-r from-cyan-50 to-blue-50 dark:from-cyan-900/20 dark:to-blue-900/20 border border-cyan-200 dark:border-cyan-700/30">
                      <h3 className="font-semibold text-base mb-3">Preventive Care</h3>
                      <div className="space-y-3">
                        <div className="flex items-center justify-between p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                          <span className="text-sm">Annual Physical</span>
                          <Badge variant="outline" className="border-green-200 text-green-700">
                            Complete
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between p-3 bg-white/60 dark:bg-gray-800/60 rounded-lg">
                          <span className="text-sm">Flu Vaccination</span>
                          <Badge variant="outline" className="border-yellow-200 text-yellow-700">
                            Due Soon
                          </Badge>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Collapsible>
          </Card>

        </div>
      </div>
    </div>
  );
}
