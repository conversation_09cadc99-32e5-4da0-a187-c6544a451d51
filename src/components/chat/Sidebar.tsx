'use client';

import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useChat } from '@/contexts/ChatContext'; // Import useChat
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { PlusCircle, MessageSquare, Edit3, Trash2, Menu, X, User, Sun, Moon, Eraser, RefreshCcw, ChevronDown, House } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { useIsMobile } from '@/hooks/use-mobile';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerContent,
  DrawerHeader,
  DrawerTitle,
  DrawerDescription,
  DrawerTrigger,
  DrawerClose,
} from "@/components/ui/drawer";
import { cn } from '@/lib/utils';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useTheme } from 'next-themes';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarTrigger,
} from "@/components/ui/sidebar"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@radix-ui/react-collapsible';
import { ForYouItem } from '@/types'; // Import ForYouItem interface
import { ProfileBtn } from "@/components/profile-btn";

export function AppSidebar() {
  const { threads, currentThreadId, createThread, selectThread, renameThread, deleteThread, isLoading, allAvailableForYouItems } = useChat(); // Destructure allAvailableForYouItems from context
  const router = useRouter();
  const [renamingThreadId, setRenamingThreadId] = useState<string | null>(null);
  const [newThreadName, setNewThreadName] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const isMobile = useIsMobile();
  const { theme, setTheme } = useTheme();

  // REMOVED: initialStaticSuggestions array is no longer here.
  // The 'For You' items will now exclusively come from `allAvailableForYouItems` from the context.

  const [visibleForYouItems, setVisibleForYouItems] = useState<ForYouItem[]>([]);
  const localStorageKey = 'usedForYouItemIds'; // This key remains important for tracking used items

  // Effect to filter and display For You items based on what's available and what's been used
  useEffect(() => {
    try {
      const storedUsedIds = localStorage.getItem(localStorageKey);
      const usedIds: string[] = storedUsedIds ? JSON.parse(storedUsedIds) : [];
      // Filter from allAvailableForYouItems received from context
      const filteredItems = allAvailableForYouItems.filter(item => !usedIds.includes(item.id));
      setVisibleForYouItems(filteredItems);
    } catch (error) {
      console.error("Failed to load 'For You' items from localStorage:", error);
      // Fallback to all available items from context if localStorage read fails,
      // though ideally, this would be handled gracefully by context loading.
      setVisibleForYouItems(allAvailableForYouItems);
    }
  }, [allAvailableForYouItems]); // Depend on allAvailableForYouItems from context, which will trigger re-filtering

  const handleForYouItemClick = useCallback((item: ForYouItem) => {
    createThread({ name: item.title, initialMessage: item.initialMessage });
    router.push('/journeys');

    try {
      const storedUsedIds = localStorage.getItem(localStorageKey);
      let usedIds: string[] = storedUsedIds ? JSON.parse(storedUsedIds) : [];
      if (!usedIds.includes(item.id)) {
        usedIds.push(item.id);
        localStorage.setItem(localStorageKey, JSON.stringify(usedIds));
      }
      setVisibleForYouItems(prevItems => prevItems.filter(visibleItem => visibleItem.id !== item.id));
    } catch (error) {
      console.error("Failed to update 'For You' items in localStorage:", error);
    }

    if (isMobile) {
      setIsOpen(false);
    }
  }, [createThread, isMobile, router]); // Added router to dependencies

  const toggleTheme = () => {
    setTheme(theme === 'dark' ? 'light' : 'dark');
  };

  // Enhanced mobile drawer management with swipe detection
  const [touchStart, setTouchStart] = useState<number | null>(null);
  const [touchEnd, setTouchEnd] = useState<number | null>(null);

  // Swipe detection for mobile drawer
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  }, []);

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    setTouchEnd(e.targetTouches[0].clientX);
  }, []);

  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd) return;

    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;

    if (isLeftSwipe && isOpen) {
      setIsOpen(false);
    }
  }, [touchStart, touchEnd, isOpen]);

  // Enhanced keyboard navigation for mobile drawer
  useEffect(() => {
    if (!isMobile || !isOpen) return;

    const handleKeyDown = (e: KeyboardEvent) => {
      // Escape to close mobile drawer
      if (e.key === 'Escape') {
        setIsOpen(false);
      }
      // Tab trapping within drawer
      if (e.key === 'Tab') {
        const focusableElements = document.querySelectorAll(
          '[data-sidebar] button, [data-sidebar] input, [data-sidebar] [tabindex]:not([tabindex="-1"])'
        );
        const firstElement = focusableElements[0] as HTMLElement;
        const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

        if (e.shiftKey && document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        } else if (!e.shiftKey && document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isMobile, isOpen]);

  const handleRename = (threadId: string) => {
    if (newThreadName.trim()) {
      renameThread(threadId, newThreadName.trim());
      setRenamingThreadId(null);
      setNewThreadName('');
    }
  };

  const handleThreadSelect = (threadId: string) => {
    selectThread(threadId);
    router.push('/journeys');
    if (isMobile) {
      setIsOpen(false); // Close mobile drawer when thread is selected
    }
  };

  const handleCreateThread = () => {
    createThread();
    router.push('/journeys');
    if (isMobile) {
      setIsOpen(false); // Close mobile drawer when new thread is created
    }
  };

  const clearLocalStorage = useCallback(() => {
    localStorage.removeItem('chatThreads');
    localStorage.removeItem('usedForYouItemIds');
    localStorage.removeItem('allAvailableForYouItems'); // NEW: Clear the dynamic For You items storage
    window.location.reload();
  }, []);

  if (isLoading) {
    return isMobile ? null : <div className="p-4 space-y-2"><p>Loading chats...</p></div>;
  }

  const SidebarContents = () => (
    <>
      <Sidebar className="p-2">
        <SidebarHeader>
          <div className="flex flex-row items-start justify-start">
            <button onClick={() => router.push('/')} className="flex flex-row items-start justify-start">
              <img src="/images/Primary_Logo_light.png" alt="Logo" className="h-[24px] w-auto dark:hidden" />
              <img src="/images/Primary_Logo.png" alt="Logo" className="h-[24px] w-auto hidden dark:block" />
            </button>
          </div>
          <Button
            onClick={handleCreateThread}
            variant="outline"
            className={cn(
              "my-4 w-full transition-all duration-200 justify-start",
              isMobile ? "min-h-[44px] text-base" : "min-h-[36px] text-sm"
            )}
            size={isMobile ? "lg" : "default"}
            aria-label="Create new chat thread"
          >
            <PlusCircle className="mr-2 h-4 w-4" aria-hidden="true" />
            New Chat
          </Button>
        </SidebarHeader>
        <SidebarContent>
          <SidebarGroup>
            <ScrollArea className="flex-grow h-[50%]" aria-label="Chat threads list">
              <div className="space-y-1 p-2 max-w-[320px]" role="list">
                {threads.length === 0 && (
                  <div className='text-sm text-muted-foreground px-2 py-4 text-center' role="status">
                    No chats yet. Start a new one!
                  </div>
                )}
                {threads.map((thread, index) => (
                  <div
                    key={thread.id}
                    className={cn(
                      "group flex items-center justify-between rounded-md cursor-pointer hover:bg-accent transition-all duration-200",
                      isMobile ? "p-3 min-h-[44px]" : "p-2 min-h-[32px]",
                      currentThreadId === thread.id ? 'bg-accent font-semibold ring-1 ring-primary/40' : ''
                    )}
                    role="listitem"
                    aria-current={currentThreadId === thread.id ? 'page' : undefined}
                  >
                    {renamingThreadId === thread.id ? (
                      <div className='flex-grow flex items-center' role="form" aria-label="Rename chat">
                        <Input
                          type="text"
                          value={newThreadName}
                          onChange={(e) => setNewThreadName(e.target.value)}
                          onBlur={() => handleRename(thread.id)}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter') handleRename(thread.id);
                            if (e.key === 'Escape') setRenamingThreadId(null);
                          }}
                          autoFocus
                          className={cn(
                            'text-sm flex-grow',
                            isMobile ? 'h-10' : 'h-8'
                          )}
                          aria-label={`Rename chat from ${thread.name}`}
                          maxLength={100}
                        />
                      </div>
                    ) : (
                      <button
                        onClick={() => handleThreadSelect(thread.id)}
                        className='flex-grow flex items-center text-left p-1 rounded hover:bg-accent/50 transition-colors'
                        aria-label={`Select chat: ${thread.name}`}
                        tabIndex={0}
                      >
                        <MessageSquare className="mr-2 h-4 w-4 flex-shrink-0" aria-hidden="true" />
                        <span className=' text-sm'>{thread.name}</span>
                      </button>
                    )}

                    <div className={cn(
                      'flex items-center transition-opacity duration-200',
                      'opacity-0 group-hover:opacity-100 group-focus-within:opacity-100',
                      isMobile && 'opacity-100' // Always visible on mobile
                    )}>
                      {renamingThreadId !== thread.id && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className={cn(
                            "transition-all duration-200 hover:bg-accent",
                            isMobile ? "h-9 w-9" : "h-7 w-7"
                          )}
                          onClick={(e) => {
                            e.stopPropagation();
                            setRenamingThreadId(thread.id);
                            setNewThreadName(thread.name);
                          }}
                          aria-label={`Rename chat: ${thread.name}`}
                        >
                          <Edit3 className="h-4 w-4" aria-hidden="true" />
                        </Button>
                      )}

                      <Dialog>
                        <DialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="icon"
                            className={cn(
                              "text-destructive hover:text-destructive hover:bg-destructive/10 transition-all duration-200",
                              isMobile ? "h-9 w-9" : "h-7 w-7"
                            )}
                            onClick={(e) => e.stopPropagation()}
                            aria-label={`Delete chat: ${thread.name}`}
                          >
                            <Trash2 className="h-4 w-4" aria-hidden="true" />
                          </Button>
                        </DialogTrigger>
                        <DialogContent role="alertdialog">
                          <DialogHeader>
                            <DialogTitle>Delete Chat?</DialogTitle>
                            <DialogDescription>
                              Are you sure you want to delete the chat "{thread.name}"? This action cannot be undone.
                            </DialogDescription>
                          </DialogHeader>
                          <DialogFooter>
                            <DialogClose asChild>
                              <Button variant="outline">Cancel</Button>
                            </DialogClose>
                            <Button
                              variant="destructive"
                              onClick={() => deleteThread(thread.id)}
                              aria-label={`Confirm delete chat: ${thread.name}`}
                            >
                              Delete
                            </Button>
                          </DialogFooter>
                        </DialogContent>
                      </Dialog>
                    </div>
                  </div>
                ))}
              </div>
              {/* Topics Section */}
              {visibleForYouItems.length > 0 && ( // Only show if there are items
                <Collapsible defaultOpen className="group/collapsible">
                  <SidebarGroup>
                    <SidebarGroupLabel asChild>
                      <CollapsibleTrigger>
                        <div className="flex flex-row text-left justify-start"> Crohn's <br></br>Management & Support</div>
                        <ChevronDown className="ml-auto transition-transform group-data-[state=open]/collapsible:rotate-180" />
                      </CollapsibleTrigger>
                    </SidebarGroupLabel>
                    <CollapsibleContent>
                      <SidebarGroupContent>
                        <div className="space-y-2 p-2 max-w-[320px]">
                          {visibleForYouItems.map((item) => (
                            <Button
                              key={item.id}
                              variant="ghost"
                              className="justify-start text-sm max-w-[220px] whitespace-pre-wrap text-left h-auto"
                              onClick={() => handleForYouItemClick(item)}
                              aria-label={`Start new chat about ${item.title}`}
                            >
                              <MessageSquare className="mr-2 h-4 w-4" />
                              {item.title}
                            </Button>
                          ))}
                        </div>
                      </SidebarGroupContent>
                    </CollapsibleContent>
                  </SidebarGroup>
                </Collapsible>
              )}

            </ScrollArea>
          </SidebarGroup>
        </SidebarContent>
        {/* Bottom buttons container */}
        <SidebarFooter>
          <div className="flex items-center justify-between mt-4 p-2 ">
            <Link href="/transcripts" passHref className='w-full'>
              <Button variant="outline" aria-label="View Transcripts" className="w-full border-none">View Transcripts</Button>
            </Link>
          </div>
          <div className="flex items-center justify-between  p-2 border-t border-b border-border/50 ">
            <div className="flex items-center gap-2">
              {/* Home Button */}
              <Link href="/" passHref>
                <Button
                  variant="ghost"
                  size="icon"
                  className={cn(
                    "h-9 w-9",
                    isMobile ? "h-10 w-10" : "h-9 w-9"
                  )}
                  onClick={() => {
                    if (isMobile) {
                      setIsOpen(false); // Close sidebar on mobile after click
                    }
                    router.push('/');
                  }}
                  aria-label="Navigate to home page"
                >
                  <House className="h-5 w-5" aria-hidden="true" />
                </Button>
              </Link>
              {/* light/dark mode button */}
              <Button
                variant="ghost"
                size="icon"
                className={cn(
                  "h-9 w-9",
                  isMobile ? "h-10 w-10" : "h-9 w-9"
                )}
                onClick={toggleTheme}
                aria-label={`Switch to ${theme === 'dark' ? 'light' : 'dark'} mode`}
              >
                {theme === 'dark' ? (
                  <Sun className="h-5 w-5" aria-hidden="true" />
                ) : (
                  <Moon className="h-5 w-5" aria-hidden="true" />
                )}
              </Button>

              {/* Internal Use Drawer */}
              <Drawer>
                <DrawerTrigger asChild>
                  <Button
                    variant="ghost"
                    size="icon"
                    className={cn(
                      "h-9 w-9",
                      isMobile ? "h-10 w-10" : "h-9 w-9"
                    )}
                    aria-label="Open Internal Use Menu"
                  >
                    <Menu className="h-5 w-5" aria-hidden="true" />
                  </Button>
                </DrawerTrigger>
                <DrawerContent>
                  <div className="mx-auto w-full max-w-sm text-center">
                    <DrawerHeader>
                      <DrawerTitle>Internal Use</DrawerTitle>
                      <DrawerDescription className="text-white">
                        Quick access to tailored use cases and journeys.
                      </DrawerDescription>
                    </DrawerHeader>
                    <div className="flex flex-col gap-3 mt-8 mb-20 px-4">
                      <DrawerClose asChild>
                      <Button
                        variant="outline"
                        className="w-full flex flex-col h-auto py-4"
                        onClick={() => {
                          router.push('/omnichannel');
                        }}
                      >
                        Virtual Concierge - Asthma Care
                        <br />
                        <span className="text-xs text-muted-foreground">Chat, Voice and Transcripts</span>
                      </Button>
                      </DrawerClose>
                      <DrawerClose asChild>
                      <Button
                        variant="outline"
                        className="w-full flex flex-col h-auto py-4"
                        onClick={() => {
                          router.push('/internal/crohns-diagnosis');
                        }}
                      >
                        Crohn's Diagnosis<br/>
                        <span className="text-xs text-muted-foreground">SMS, Diagnosis Confirmation, Personalization</span>
                      </Button>
                      </DrawerClose>
                      <DrawerClose asChild>
                      <Button
                        variant="outline"
                        className="w-full flex flex-col h-auto py-4"
                        onClick={() => {
                          router.push('/internal/prior-auth-steerage');
                        }}
                      >
                        Prior Auth and Steerage<br/>
                        <span className="text-xs text-muted-foreground">Email, Benefits Awareness, Cost-Saving</span>
                      </Button>
                      </DrawerClose>
                      <DrawerClose asChild>
                      <Button
                        variant="outline"
                        className="w-full flex flex-col h-auto py-4"
                        onClick={() => {
                          router.push('/internal/clinical-outreach');
                        }}
                      >
                        Post Procedure Clinical Outreach<br/>
                        <span className="text-xs text-muted-foreground">Voice Assistant, Outbound Call, Post-Op</span>
                      </Button>
                      </DrawerClose>
                    </div>
                  </div>
                </DrawerContent>
              </Drawer>
            </div>
            {/* Clear Storage Button */}
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-9 w-9 text-xs",
                isMobile ? "h-10 w-10" : "h-9 w-9"
              )}
              onClick={clearLocalStorage}
              aria-label="Clear local storage (dev only)"
            > Reset
            </Button>
          </div>
          <ProfileBtn />
        </SidebarFooter>

      </Sidebar>
    </>
  );



  // Tablet and Desktop: Render as normal sidebar
  return <SidebarContents />;
}