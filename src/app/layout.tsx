"use client";
import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { ChatProvider } from "@/contexts/ChatContext";
import { Toaster } from "@/components/ui/sonner";
import { ThemeProvider } from "@/components/ThemeProvider";
import { SidebarProvider } from "@/components/ui/sidebar";
import { AppSidebar } from "@/components/chat/Sidebar";
import { CustomSidebarTrigger } from "@/components/ui/CustomSidebarTrigger";
import { StagewiseToolbar } from "@stagewise/toolbar-next";
import { PostHogProvider } from "@/app/posthog/posthog-provider";
import { LayoutProvider, useLayout } from "@/contexts/LayoutContext";
import { Switch } from "@/components/ui/switch";
import { CareCanvas } from "@/components/care-canvas/CareCanvas";
import {
  Drawer,
  DrawerContent,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { But<PERSON> } from "@/components/ui/button";
import React from "react";
import JourneysPage from "./journeys/page";


const inter = Inter({ subsets: ["latin"] });
const stagewiseConfig = {
  plugins: [],
};

// export const metadata: Metadata = {
//   title: "Virtual Concierge",
//   description: "The Future of Healthcare",
// };

function LayoutContent({ children }: { children: React.ReactNode }) {
  const { isExperimentalLayout, toggleLayout } = useLayout();

  return (
    <>
      <div className="absolute top-4 right-4 z-50 flex items-center space-x-2">
        <Switch
          id="layout-switch"
          checked={isExperimentalLayout}
          onCheckedChange={toggleLayout}
        />
        <label htmlFor="layout-switch">Experimental Layout</label>
      </div>
      {isExperimentalLayout ? (
        <div className="relative h-screen w-screen">
          <CareCanvas />
          <Drawer>
            <DrawerTrigger asChild>
              <Button variant="outline" className="absolute bottom-4 right-4">
                Open Chat
              </Button>
            </DrawerTrigger>
            <DrawerContent>
              <div className="min-h-[200vh]! max-h-[200vh]! ">
                <SidebarProvider>
                  <AppSidebar />
                  <CustomSidebarTrigger />
                  <main className="flex-grow w-full overflow-auto bg-genui">
                    <JourneysPage />
                  </main>
                </SidebarProvider>
              </div>
            </DrawerContent>
          </Drawer>
        </div>
      ) : (
        <SidebarProvider>
          <AppSidebar />
          <CustomSidebarTrigger />
          <main className="flex-grow w-full overflow-auto bg-genui">
            {children}
            <Toaster />
            {process.env.NODE_ENV === "development" && (
              <StagewiseToolbar config={stagewiseConfig} />
            )}
          </main>
        </SidebarProvider>
      )}
    </>
  );
}

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          disableTransitionOnChange
        >
          <PostHogProvider>
            <ChatProvider>
              <LayoutProvider>
                <LayoutContent>{children}</LayoutContent>
              </LayoutProvider>
            </ChatProvider>
          </PostHogProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
