"use client";
import React, { createContext, useState, useContext, ReactNode } from 'react';

interface LayoutContextType {
  isExperimentalLayout: boolean;
  toggleLayout: () => void;
}

const LayoutContext = createContext<LayoutContextType | undefined>(undefined);

export const LayoutProvider = ({ children }: { children: ReactNode }) => {
  const [isExperimentalLayout, setIsExperimentalLayout] = useState(false);

  const toggleLayout = () => {
    setIsExperimentalLayout(prev => !prev);
  };

  return (
    <LayoutContext.Provider value={{ isExperimentalLayout, toggleLayout }}>
      {children}
    </LayoutContext.Provider>
  );
};

export const useLayout = () => {
  const context = useContext(LayoutContext);
  if (context === undefined) {
    throw new Error('useLayout must be used within a LayoutProvider');
  }
  return context;
};
